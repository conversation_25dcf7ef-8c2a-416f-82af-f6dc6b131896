import uuid
import time

from PySide6.QtCore import QObject, Signal

class GlobalSignals(QObject):
    """ 全局信号管理 """
    
    log_changed_signal = Signal(str)
    mes_info_signal = Signal(str, str)
    info_dialog_signal = Signal(str, str)
    thread_signal = Signal(str, str)
    error_dialog_signal = Signal(str)
    table_create_signal = Signal(list)
    table_update_single_row_signal = Signal(tuple, dict)
    table_create_single_row_signal = Signal(dict)
    table_change_row_color_signal = Signal(tuple)

    # dataChanged = Signal(str)  # 当数据变化时触发
    # buttonClicked = Signal(str)  # 当用户点击按钮时触发
    def __init__(self):
        super(GlobalSignals, self).__init__()
        self.message_result = {}

    def get_uuid(self):
        return uuid.uuid4()

    def set_message_result(self, uuid, result):
        self.message_result[uuid] = result

    def wait_message_result(self, uuid, time_out=3600):
        time0 = time.time()
        while time.time() - time0 < time_out:
            if uuid in self.message_result.keys():
                break
            time.sleep(1)
        else:
            return TimeoutError
        return self.message_result.pop(uuid)

# 创建单例信号实例
signals = GlobalSignals()
