save_config_list = [
    {
        "name": "TempMonValue",
        "show_name": "TempMonValue",
        "page": 0x00,
        "start_reg": 14,
        "end_reg": 15,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": 75,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "degC",
        "value_type": "S16",
        "parent_name": "MODULE",
    },
    {
        "name": "VccMonVoltage",
        "show_name": "VccMonVoltage",
        "page": 0x00,
        "start_reg": 16,
        "end_reg": 17,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": 3.135,
        "max_value": 3.465,
        "zoom_way": "division",
        "coefficient": 10000,
        "unit": "V",
        "value_type": "U16",
        "parent_name": "MODULE",
    },
    {
        "name": "Aux1MonValue(CSTAR Temperature)",
        "show_name": "Aux1MonValue(CSTAR Temperature)",
        "page": 0x00,
        "start_reg": 18,
        "end_reg": 19,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "degC",
        "value_type": "S16",
        "parent_name": "MODULE",
    },
    {
        "name": "Aux2MonValue(TEC Current)",
        "show_name": "Aux2MonValue(TEC Current)",
        "page": 0x00,
        "start_reg": 20,
        "end_reg": 21,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_decimal",
        "parent_name": "MODULE",
    },
    {
        "name": "Aux3MonValue(Laser Temperature)",
        "show_name": "Aux3MonValue(Laser Temperature)",
        "page": 0x00,
        "start_reg": 22,
        "end_reg": 23,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "degC",
        "value_type": "S16",
        "parent_name": "MODULE",
    },
    {
        "name": "CustomMonValue(DSP Temperature)",
        "show_name": "CustomMonValue(DSP Temperature)",
        "page": 0x00,
        "start_reg": 24,
        "end_reg": 25,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "degC",
        "value_type": "S16",
        "parent_name": "MODULE",
    },
    {
        "name": "LaserBiasTx1",
        "show_name": "LaserBiasTx1",
        "page": 0x11,
        "start_reg": 170,
        "end_reg": 171,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.008,
        "unit": "mA",
        "value_type": "U16",
        "parent_name": "MODULE",
    },
    {
        "name": "Laser Age (0% at BOL, 100% EOL) (Data Path)",
        "show_name": "Laser Age (0% at BOL, 100% EOL) (Data Path)",
        "page": 0x24,
        "start_reg": 128,
        "end_reg": 129,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": 1,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "TEC Current (Module) Basic",
        "show_name": "TEC Current (Module) Basic",
        "page": 0x24,
        "start_reg": 130,
        "end_reg": 131,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 327.67,
        "unit": "%",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Laser Frequency Error (Media Lane) Basic",
        "show_name": "Laser Frequency Error (Media Lane) Basic",
        "page": 0x24,
        "start_reg": 132,
        "end_reg": 133,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 10,
        "unit": "MHz",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Laser Temperature (Media Lane) Basic",
        "show_name": "Laser Temperature (Media Lane) Basic",
        "page": 0x24,
        "start_reg": 134,
        "end_reg": 135,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "degC",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "eSNR Media Input (Media Lane) Basic",
        "show_name": "eSNR Media Input (Media Lane) Basic",
        "page": 0x24,
        "start_reg": 136,
        "end_reg": 137,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 256,
        "unit": "dB",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Pre-FEC BER Current Sample Media Input (Data Path) Basic",
        "show_name": "Pre-FEC BER Current Sample Media Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 144,
        "end_reg": 145,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "FERC Current Sample Value Media Input (Data Path) Basic",
        "show_name": "FERC Current Sample Value Media Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 152,
        "end_reg": 153,
        "is_control_value": True,
        "is_show_value": False,
        "min_value": 0,
        "max_value": 0,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "FERC Total Accumulated Media Input (Data Path) Statistic",
        "show_name": "FERC Total Accumulated Media Input (Data Path) Statistic",
        "page": 0x24,
        "start_reg": 154,
        "end_reg": 155,
        "is_control_value": True,
        "is_show_value": False,
        "min_value": 0,
        "max_value": 0,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "HOST_IF1_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "show_name": "HOST_IF1_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 162,
        "end_reg": 163,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "HOST_IF1_FERC Current Sample Value Host Input (Data Path) Basic",
        "show_name": "HOST_IF1_FERC Current Sample Value Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 170,
        "end_reg": 171,
        "is_control_value": True,
        "is_show_value": False,
        "min_value": 0,
        "max_value": 0,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "HOST_IF1_FERC Total Accumulated Host Input (Data Path) Statistic",
        "show_name": "HOST_IF1_FERC Total Accumulated Host Input (Data Path) Statistic",
        "page": 0x24,
        "start_reg": 172,
        "end_reg": 173,
        "is_control_value": True,
        "is_show_value": False,
        "min_value": 0,
        "max_value": 0,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "HOST_IF2_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "show_name": "HOST_IF2_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 180,
        "end_reg": 181,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 2,
    },
    {
        "name": "HOST_IF2_FERC Current Sample Value Host Input (Data Path) Basic",
        "show_name": "HOST_IF2_FERC Current Sample Value Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 188,
        "end_reg": 189,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 2,
    },
    {
        "name": "HOST_IF2_FERC Total Accumulated Host Input (Data Path) Statistic",
        "show_name": "HOST_IF2_FERC Total Accumulated Host Input (Data Path) Statistic",
        "page": 0x24,
        "start_reg": 190,
        "end_reg": 191,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 2,
    },
    {
        "name": "HOST_IF3_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "show_name": "HOST_IF3_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 198,
        "end_reg": 199,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 4,
    },
    {
        "name": "HOST_IF3_FERC Current Sample Value Host Input (Data Path) Basic",
        "show_name": "HOST_IF3_FERC Current Sample Value Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 206,
        "end_reg": 207,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 4,
    },
    {
        "name": "HOST_IF3_FERC Total Accumulated Host Input (Data Path) Statistic",
        "show_name": "HOST_IF3_FERC Total Accumulated Host Input (Data Path) Statistic",
        "page": 0x24,
        "start_reg": 208,
        "end_reg": 209,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 4,
    },
    {
        "name": "HOST_IF4_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "show_name": "HOST_IF4_Pre-FEC BER Current Sample Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 216,
        "end_reg": 217,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 6,
    },
    {
        "name": "HOST_IF4_FERC Current Sample Value Host Input (Data Path) Basic",
        "show_name": "HOST_IF4_FERC Current Sample Value Host Input (Data Path) Basic",
        "page": 0x24,
        "start_reg": 224,
        "end_reg": 225,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 6,
    },
    {
        "name": "HOST_IF4_FERC Total Accumulated Host Input (Data Path) Statistic",
        "show_name": "HOST_IF4_FERC Total Accumulated Host Input (Data Path) Statistic",
        "page": 0x24,
        "start_reg": 226,
        "end_reg": 227,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F16",
        "parent_name": "VDM",
        "monitored_resource": 6,
    },
    {
        "name": "Laser_Fault_State",
        "show_name": "Laser_Fault_State",
        "page": 0x24,
        "start_reg": 228,
        "end_reg": 229,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Laser_Warning_State",
        "show_name": "Laser_Warning_State",
        "page": 0x24,
        "start_reg": 230,
        "end_reg": 231,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "RxLo_Power",
        "show_name": "RxLo_Power",
        "page": 0x24,
        "start_reg": 232,
        "end_reg": 233,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "dBm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias X/I",
        "show_name": "Modulator Bias X/I",
        "page": 0x25,
        "start_reg": 128,
        "end_reg": 129,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias X/Q",
        "show_name": "Modulator Bias X/Q",
        "page": 0x25,
        "start_reg": 130,
        "end_reg": 131,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias Y/I",
        "show_name": "Modulator Bias Y/I",
        "page": 0x25,
        "start_reg": 132,
        "end_reg": 133,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias Y/Q",
        "show_name": "Modulator Bias Y/Q",
        "page": 0x25,
        "start_reg": 134,
        "end_reg": 135,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias X_Phase",
        "show_name": "Modulator Bias X_Phase",
        "page": 0x25,
        "start_reg": 136,
        "end_reg": 137,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Modulator Bias Y_Phase",
        "show_name": "Modulator Bias Y_Phase",
        "page": 0x25,
        "start_reg": 138,
        "end_reg": 139,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "CD _high granularity, short link: Note2",
        "show_name": "CD _high granularity, short link: Note2",
        "page": 0x25,
        "start_reg": 140,
        "end_reg": 141,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "Ps/nm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "CD _ low granularity, long link: Note2",
        "show_name": "CD _ low granularity, long link: Note2",
        "page": 0x25,
        "start_reg": 142,
        "end_reg": 143,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 20,
        "unit": "Ps/nm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "DGD",
        "show_name": "DGD",
        "page": 0x25,
        "start_reg": 144,
        "end_reg": 145,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "Ps",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "SOPMD - high granularity",
        "show_name": "SOPMD - high granularity",
        "page": 0x25,
        "start_reg": 146,
        "end_reg": 147,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "Ps^2",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "PDL",
        "show_name": "PDL",
        "page": 0x25,
        "start_reg": 148,
        "end_reg": 149,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.1,
        "unit": "dB",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "OSNR",
        "show_name": "OSNR",
        "page": 0x25,
        "start_reg": 150,
        "end_reg": 151,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.1,
        "unit": "dB",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "eSNR",
        "show_name": "eSNR",
        "page": 0x25,
        "start_reg": 152,
        "end_reg": 153,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.1,
        "unit": "dB",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "CFO",
        "show_name": "CFO",
        "page": 0x25,
        "start_reg": 154,
        "end_reg": 155,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "MHz",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "EVM_modem",
        "show_name": "EVM_modem",
        "page": 0x25,
        "start_reg": 156,
        "end_reg": 157,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "division",
        "coefficient": 655.35,
        "unit": "%",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Tx Power",
        "show_name": "Tx Power",
        "page": 0x25,
        "start_reg": 158,
        "end_reg": 159,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "dBm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Rx Total Power",
        "show_name": "Rx Total Power",
        "page": 0x25,
        "start_reg": 160,
        "end_reg": 161,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "dBm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Rx Signal Power",
        "show_name": "Rx Signal Power",
        "page": 0x25,
        "start_reg": 162,
        "end_reg": 163,
        "is_control_value": False,
        "is_show_value": True,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "dBm",
        "value_type": "S16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "SOP ROC",
        "show_name": "SOP ROC",
        "page": 0x25,
        "start_reg": 164,
        "end_reg": 165,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 1,
        "unit": "krad/s",
        "value_type": "U16",
        "parent_name": "VDM",
        "monitored_resource": 0,
    },
    {
        "name": "Current value of Q factor",
        "show_name": "Current value of Q factor",
        "page": 0x35,
        "start_reg": 240,
        "end_reg": 241,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.1,
        "unit": "dB",
        "value_type": "U16",
        "parent_name": "VDM_ORI",
        "monitored_resource": 0,
    },
    {
        "name": "Rx_Lo_power",
        "show_name": "Rx_Lo_power",
        "page": 0x35,
        "start_reg": 254,
        "end_reg": 255,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "multiply",
        "coefficient": 0.01,
        "unit": "dBm",
        "value_type": "S16",
        "parent_name": "VDM_ORI",
        "monitored_resource": 0,
    },
    {
        "name": "LD/RD tx alarm",
        "show_name": "LD/RD tx alarm",
        "page": 0x3B,
        "start_reg": 193,
        "end_reg": 193,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "PCS",
    },
    {
        "name": "tx PCS alarm LOA/RF/LF",
        "show_name": "tx PCS alarm LOA/RF/LF",
        "page": 0x3B,
        "start_reg": 195,
        "end_reg": 195,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "PCS",
    },
    {
        "name": "rx PCS alarm LOA/RF/LF",
        "show_name": "rx PCS alarm LOA/RF/LF",
        "page": 0x3B,
        "start_reg": 196,
        "end_reg": 196,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "PCS",
    },
    {
        "name": "tx PCS alarm HIBER/HISER/LOBL",
        "show_name": "tx PCS alarm HIBER/HISER/LOBL",
        "page": 0x3B,
        "start_reg": 248,
        "end_reg": 248,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "PCS",
    },
    {
        "name": "rx PCS alarm HIBER/HISER/LOBL",
        "show_name": "rx PCS alarm HIBER/HISER/LOBL",
        "page": 0x3B,
        "start_reg": 249,
        "end_reg": 249,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "PCS",
    },
    {
        "name": "DSP HI Amplitude",
        "show_name": "DSP HI Amplitude",
        "page": 0xB5,
        "start_reg": 140,
        "end_reg": 140,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_decimal",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "DSP HQ Amplitude",
        "show_name": "DSP HQ Amplitude",
        "page": 0xB5,
        "start_reg": 141,
        "end_reg": 141,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_decimal",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "DSP VI Amplitude",
        "show_name": "DSP VI Amplitude",
        "page": 0xB5,
        "start_reg": 142,
        "end_reg": 142,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_decimal",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "DSP VQ Amplitude",
        "show_name": "DSP VQ Amplitude",
        "page": 0xB5,
        "start_reg": 143,
        "end_reg": 143,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_decimal",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "tx_gc_xi_pwr",
        "show_name": "tx_gc_xi_pwr",
        "page": 0xB5,
        "start_reg": 232,
        "end_reg": 233,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "dBm",
        "value_type": "gc_optical_pwr",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "tx_gc_xq_pwr",
        "show_name": "tx_gc_xq_pwr",
        "page": 0xB5,
        "start_reg": 234,
        "end_reg": 235,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "dBm",
        "value_type": "gc_optical_pwr",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "tx_gc_yi_pwr",
        "show_name": "tx_gc_yi_pwr",
        "page": 0xB5,
        "start_reg": 236,
        "end_reg": 237,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "dBm",
        "value_type": "gc_optical_pwr",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "tx_gc_yq_pwr",
        "show_name": "tx_gc_yq_pwr",
        "page": 0xB5,
        "start_reg": 238,
        "end_reg": 239,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": "dBm",
        "value_type": "gc_optical_pwr",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "Rx_X_VOA",
        "show_name": "Rx_X_VOA",
        "page": 0xB5,
        "start_reg": 240,
        "end_reg": 243,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F32",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "Rx_Y_VOA",
        "show_name": "Rx_Y_VOA",
        "page": 0xB5,
        "start_reg": 244,
        "end_reg": 247,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "F32",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "Rx_X_Pwr",
        "show_name": "Rx_X_Pwr",
        "page": 0xB5,
        "start_reg": 248,
        "end_reg": 251,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "mW_to_dBm",
        "coefficient": None,
        "unit": "dBm",
        "value_type": "F32",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "Rx_Y_Pwr",
        "show_name": "Rx_Y_Pwr",
        "page": 0xB5,
        "start_reg": 252,
        "end_reg": 255,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": "mW_to_dBm",
        "coefficient": None,
        "unit": "dBm",
        "value_type": "F32",
        "parent_name": "DEBUG INFO",
    },
    {
        "name": "Private alarm",
        "show_name": "Private alarm",
        "page": 0xB8,
        "start_reg": 128,
        "end_reg": 146,
        "is_control_value": False,
        "is_show_value": False,
        "min_value": None,
        "max_value": None,
        "zoom_way": None,
        "coefficient": None,
        "unit": None,
        "value_type": "ori_hex",
        "parent_name": "DEBUG INFO",
    },
]

save_key_vdm_address_key_map = {
    "Laser Age (0% at BOL, 100% EOL) (Data Path)": "Laser_Age",
    "TEC Current (Module) Basic": "TEC_Current",
    "Laser Frequency Error (Media Lane) Basic": "Laser_Frequency_Error",
    "Laser Temperature (Media Lane) Basic": "Laser_Temperature",
    "eSNR Media Input (Media Lane) Basic": "eSNR_Media_Input",
    "Pre-FEC BER Current Sample Media Input (Data Path) Basic": "Pre_FEC_BER_Current_Value_Media_Input",
    "FERC Current Sample Value Media Input (Data Path) Basic": "FERC_Current_Value_Media_Input",
    "FERC Total Accumulated Media Input (Data Path) Statistic": "FERC_Total_Accumulated_Media_Input",
    "HOST_IF1_Pre-FEC BER Current Sample Host Input (Data Path) Basic": "Pre_FEC_BER_Current_Value_Host_Input",
    "HOST_IF1_FERC Current Sample Value Host Input (Data Path) Basic": "FERC_Current_Value_Host_Input",
    "HOST_IF1_FERC Total Accumulated Host Input (Data Path) Statistic": "FERC_Total_Accumulated_Host_Input",
    "HOST_IF2_Pre-FEC BER Current Sample Host Input (Data Path) Basic": "Pre_FEC_BER_Current_Value_Host_Input",
    "HOST_IF2_FERC Current Sample Value Host Input (Data Path) Basic": "FERC_Current_Value_Host_Input",
    "HOST_IF2_FERC Total Accumulated Host Input (Data Path) Statistic": "FERC_Total_Accumulated_Host_Input",
    "HOST_IF3_Pre-FEC BER Current Sample Host Input (Data Path) Basic": "Pre_FEC_BER_Current_Value_Host_Input",
    "HOST_IF3_FERC Current Sample Value Host Input (Data Path) Basic": "FERC_Current_Value_Host_Input",
    "HOST_IF3_FERC Total Accumulated Host Input (Data Path) Statistic": "FERC_Total_Accumulated_Host_Input",
    "HOST_IF4_Pre-FEC BER Current Sample Host Input (Data Path) Basic": "Pre_FEC_BER_Current_Value_Host_Input",
    "HOST_IF4_FERC Current Sample Value Host Input (Data Path) Basic": "FERC_Current_Value_Host_Input",
    "HOST_IF4_FERC Total Accumulated Host Input (Data Path) Statistic": "FERC_Total_Accumulated_Host_Input",
    "Laser_Fault_State": "LASER_20h_FW",
    "Laser_Warning_State": "LASER_21h_FW",
    "RxLo_Power": "LASER_Output_Power",
    "Modulator Bias X/I": "Modulator_Bias_XI",
    "Modulator Bias X/Q": "Modulator_Bias_XQ",
    "Modulator Bias Y/I": "Modulator_Bias_YI",
    "Modulator Bias Y/Q": "Modulator_Bias_YQ",
    "Modulator Bias X_Phase": "Modulator_Bias_X_Phase",
    "Modulator Bias Y_Phase": "Modulator_Bias_Y_Phase",
    "CD _high granularity, short link: Note2": "CD_high_granularity",
    "CD _ low granularity, long link: Note2": "CD_low_granularity",
    "DGD": "DGD",
    "SOPMD - high granularity": "SOPMD",
    "PDL": "PDL",
    "OSNR": "OSNR",
    "eSNR": "eSNR",
    "CFO": "CFO",
    "EVM_modem": "EVM",
    "Tx Power": "Tx_Power",
    "Rx Total Power": "Rx_Total_Power",
    "Rx Signal Power": "Rx_Signal_Power",
    "SOP ROC": "SOP_ROC",
    "Current value of Q factor": "Q_factor"
}
