"""
@Time        : 2025/6/17 10:03
<AUTHOR> 53211
@File        : serial_connection.py
@Project     : oct_mcm_o2
@Description : 
"""
import time
import logging
from typing import Optional
import threading
from queue import Queue

try:
    import serial
except ImportError:
    serial = None

logger = logging.getLogger(__name__)


class SerialConnection:
    """串口连接实现类"""

    def __init__(
            self,
            port: str,
            baudrate: int = 9600,
            timeout: int = 10,
            bytesize: int = 8,
            parity: str = 'N',
            stopbits: int = 1,
            read_timeout: float = 0.1
    ):
        """
        初始化串口连接
        Args:
            port: 串口号，如 "COM3" 或 "/dev/ttyUSB0"
            baudrate: 波特率
            timeout: 超时时间(秒)
            bytesize: 数据位
            parity: 校验位 ('N'=无, 'E'=偶, 'O'=奇)
            stopbits: 停止位
        """
        if serial is None:
            raise ImportError("pyserial库未安装，请运行: pip install pyserial")
        self.response_queue = Queue()
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.bytesize = bytesize
        self.parity = parity
        self.stopbits = stopbits
        self.read_timeout = read_timeout
        self._connection_status = False
        self._serial: Optional[serial.Serial] = None

    def connect(self) -> bool:
        """建立串口连接"""
        try:
            self._serial = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                timeout=self.timeout,
                bytesize=self.bytesize,
                parity=self.parity,
                stopbits=self.stopbits
            )

            # 清空缓冲区
            self._serial.reset_input_buffer()
            self._serial.reset_output_buffer()

            self._connection_status = True

            # 启动接收线程
            self.receive_thread = threading.Thread(target=self._receive_loop)
            self.receive_thread.daemon = True
            self.receive_thread.start()

            logger.info(f"串口连接成功: {self.connection_info}")
            return True

        except Exception as e:
            logger.error(f"串口连接失败: {self.connection_info}, 错误: {e}")
            self._connection_status = False
            return False

    def _receive_loop(self):
        """接收数据循环"""
        buffer = ""
        while self._connection_status:
            if not self._serial or not self._serial.is_open:
                break
            try:
                if self._serial.in_waiting:
                    data = self._serial.read(self._serial.in_waiting)
                    try:
                        text = data.decode('utf-8')
                        buffer += text

                        # 处理完整的行
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.rstrip('\r')
                            if "<Accelink>" in line:
                                self.send_command("sys")
                            self.response_queue.put(line + '\n')

                        # 处理不完整的行（可能是提示符）
                        if buffer and (']' in buffer or '>' in buffer or '#' in buffer):
                            self.response_queue.put(buffer)
                            buffer = ""

                        # 检查是否有 --More-- 提示
                        if '--More--' in buffer or '--more--' in buffer.lower():
                            self._serial.write(b' ')
                            buffer = buffer.replace('--More--', '').replace('--more--', '')

                    except UnicodeDecodeError:
                        print(f"收到二进制数据: {data.hex()}")
                        buffer = ""

            except Exception as e:
                print(f"接收数据错误: {e}")
                break

            time.sleep(0.01)

    def send_command(self, command: str, encoding: str = "utf-8") -> str:
        """
        发送命令并获取响应
        Args:
            command: 要发送的命令
            encoding: 编码格式
        Returns:
            str: 设备响应
        """
        if not self._serial or not self._serial.is_open:
            raise ConnectionError("串口未连接")

        try:
            # 发送命令
            command_bytes = f'{command}\r\n'.encode(encoding)
            self._serial.write(command_bytes)
            self._serial.flush()

            # 读取响应, 处理'More'提示符
            response = self._read_response_with_prompt()

            return response

        except Exception as e:
            logger.error(f"发送命令失败: {command}, 错误: {e}")
            raise

    def _read_response_with_prompt(self) -> str:
        """
        读取串口响应数据，直到检测到命令提示符或超时
        """
        # 清空响应队列
        while not self.response_queue.empty():
            self.response_queue.get()
        response_lines = ""
        start_time = time.time()
        while time.time() - start_time < 2:
            try:
                response = self.response_queue.get(timeout=0.1)
                if response:
                    response_lines += response
            except Exception as e:
                # 队列超时，继续等待
                continue

        return response_lines

    def close(self) -> None:
        """关闭串口连接"""
        if self._serial and self._serial.is_open:
            self._serial.close()
            logger.info(f"串口连接已关闭: {self.connection_info}")

        self._connection_status = False
        self._serial = None

    @property
    def connection_status(self) -> bool:
        """获取连接状态"""
        return self._connection_status and self._serial and self._serial.is_open

    @property
    def connection_info(self) -> str:
        """获取连接信息"""
        return f"串口: {self.port}, 波特率: {self.baudrate}"

    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close()


if __name__ == '__main__':
    serial_client = SerialConnection("com3", 115200)
    serial_client.connect()
    # print(serial_client.send_command("sys"))
    # print(serial_client.send_command("slot 1/1"))
    print(serial_client.send_command("display cfp2-dco"))
