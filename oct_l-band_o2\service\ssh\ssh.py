import time

import paramiko


class SshConnection:
    def __init__(
            self,
            ip: str,
            port: int = 22,
            username: str = "admin",
            password: str = "Admin_123",
            timeout: int = 10,
    ):
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password
        self.timeout = timeout
        self.connection_status = False
        self._client = paramiko.SSHClient()
        self._client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        self._shell = None

    def connect(self):
        self._client.connect(
            hostname=self.ip,
            port=self.port,
            username=self.username,
            password=self.password,
            timeout=self.timeout,
        )
        self._shell = self._client.invoke_shell()  # 真正分配 pty，会加载所有登录脚本
        time.sleep(1)
        if self._shell.recv_ready():
            self._shell.recv(65535)  # 清掉欢迎信息
        self.connection_status = True

    def close(self):
        self._client.close()
        self._shell = None
        self.connection_status = False

    def _recv_all_data(self, encoding="utf-8"):
        output_list = []

        while self._shell.recv_ready():
            recv_data = self._shell.recv(65536).decode(encoding=encoding, errors='ignore')
            print(f"recv_data:{recv_data}")
            # 检查是否有 --More-- 提示
            if '--More--' in recv_data or '--more--' in recv_data.lower():
                self._shell.send(" ")
                time.sleep(0.5)
                recv_data += self._shell.recv(65536).decode(encoding=encoding, errors='ignore')
                print(f"recv_data:{recv_data}")

            output_list.append(recv_data)
        output_str = ''.join(output_list)
        return output_str

    def send_command(self, command: str) -> str:
        self._shell.send(f'{command} \n')
        time.sleep(0.5)
        output_str = self._recv_all_data()
        if 'Login:' in output_str:
            self._shell.send(f'admin \n')
            self._shell.send(f'Admin_123 \n')
            self._shell.send(f'sys \n')
            self._shell.send(f'{command} \n')
            output_str = self._recv_all_data()
        if '<' in output_str and '>' in output_str:
            self._shell.send(f'sys \n')
            self._shell.send(f'{command} \n')
            output_str = self._recv_all_data()
        return output_str


if __name__ == "__main__":
    ssh_conn = SshConnection("**************")
    ssh_conn.connect()
    result_sys = ssh_conn.send_command("sys")
    print("result_sys", result_sys)
    result_slot_1_1 = ssh_conn.send_command("slot 1/1")
    print(f"result_slot_1_1:{result_slot_1_1}")
    result_dco_cfp2 = ssh_conn.send_command("display cfp2-dco")
    print(f"result_dco_cfp2:{result_dco_cfp2}")
    # # if result_dco_cfp2.strip() == "The cfp2dco Module No.1 module is absent!":
    # #     print("插槽 1/1 为空")
    # result_exit = ssh_conn.send_command("exit")
    # result_slot_1_2 = ssh_conn.send_command("slot 1/2")
    # print(result_slot_1_2)
    # result_dco_cfp2 = ssh_conn.send_command("display cfp2-dco")
    # print(result_dco_cfp2)
    # result_exit = ssh_conn.send_command("exit")
    # ssh_conn.close()
    # result_sys = ssh_conn.send_command("sys")
    # if result_sys == r'sys \r\n[ACCELINK]':
    #     print("连接ssh成功")
    # for index in range(1, 9):
    #     slot_str = f"1/{index}"
    #     slot_command = f"slot {slot_str}"
    #     result_slot:str = ssh_conn.send_command(slot_command)
    #     result_display:str = ssh_conn.send_command("display cfp2-dco")
    #     if "The cfp2dco Module No.1 module is absent!" in result_display:
    #         print(f"插槽{slot_str}上没有模块")
    #         ssh_conn.send_command("exit")
    #         continue
    #     # 记录数据,分析数据放到循环结束后一起进行
    #     ssh_conn.send_command("exit")
    ssh_conn.close()
