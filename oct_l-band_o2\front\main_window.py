import sys

from PySide6.QtWidgets import QMainWindow, QMessageBox, QApplication, QLineEdit

from front.views.ui_main_window import Ui_MainWindow
from front.controllers.main_controller import MainController  # 逻辑控制器
from front.core.signals import signals
from config import global_store
from front.core.const import IP_BASE_STR, PORT_BASE_STR, SLOT_BASE_STR
from front.models.table_monitor_model import MyTableModel
from front.style import CenteredDelegate

from service.common.my_logger import logger


class MainWindow(QMainWindow, Ui_MainWindow):
    def __init__(self):
        super().__init__()
        self.setupUi(self)  # 设置 UI 组件

        self.controller = MainController(self)  # 绑定控制器

        # 根据配置文件创建连接配置界面
        self.create_module_connection_ui()

        # 绑定表格Model
        self.bind_table_model()

        # **在 UI 逻辑中绑定按钮事件**
        self.setup_connections()

        # 初始化后打印版本信息
        ver_info = global_store.get_config("app_ver")
        logger.info(f"app_ver: {ver_info}")
        # 获取OCT设备IP,看下进程获取的IP是否正常
        oct_ip = global_store.get_context("oct_ip")
        oct_ssh_port = global_store.get_context("oct_ssh_port")
        logger.info(f"oct_ip: {oct_ip}, oct_ssh_port: {oct_ssh_port}")
        self.setWindowTitle(f"{oct_ip}:{oct_ssh_port} L Band O2 挂机上位机")
        self.pushButton_stop.hide()

    def setup_connections(self):
        """绑定 UI 按钮到控制器处理方法"""
        # self.button.clicked.connect(self.controller.handle_button_click)  # 交给控制器处理
        signals.log_changed_signal.connect(self.controller.handle_log_changed_sig)
        signals.mes_info_signal.connect(self.controller.on_change_mes_info)
        signals.info_dialog_signal.connect(self.controller.show_info_message)
        signals.error_dialog_signal.connect(self.show_error_message)
        signals.table_create_signal.connect(self.controller.create_monitor_table)
        signals.table_update_single_row_signal.connect(self.controller.update_monitor_table_single_row)
        signals.table_create_single_row_signal.connect(self.controller.create_monitor_table_single_row)
        signals.table_change_row_color_signal.connect(self.controller.change_row_color)

        self.pushButton_login.clicked.connect(self.controller.on_mes_login_logout)
        # self.pushButton_add_config_one.clicked.connect(
        #     self.controller.add_one_empty_config
        # )
        # self.pushButton_add_config_one.hide()
        # self.pushButton_remove_config_one.clicked.connect(self.remove_connection_config_one)
        # self.pushButton_remove_config_one.hide()
        self.pushButton_save_config.clicked.connect(self.controller.save_connection_config)
        self.pushButton_start.clicked.connect(self.controller.start_test)
        self.pushButton_stop.clicked.connect(self.controller.stop_test)
        self.pushButton_finish.clicked.connect(self.controller.finish_test)

    def bind_table_model(self):
        table_headers = global_store.get_config("table_header")
        monitor_model = MyTableModel([], table_headers)
        self.table_view_monitor_model = monitor_model
        self.tableView_monitor.setModel(monitor_model)
        header = self.tableView_monitor.horizontalHeader()
        for index, header_dict in enumerate(table_headers):
            header.resizeSection(index, header_dict["width"])
        delegate = CenteredDelegate()
        self.tableView_monitor.setItemDelegate(delegate)


    def create_module_connection_ui(self):
        """ config_list = global_store.get_config("config_list")
        for config_index, config_connection in enumerate(config_list):
            ip_obj_name = f"{IP_BASE_STR}{config_index}"
            port_obj_name = f"{PORT_BASE_STR}{config_index}"

            for obj_index, (obj_key, obj_name) in enumerate(
                [
                    ("ip", ip_obj_name),
                    ("port", port_obj_name),
                ]
            ):
                obj_value = config_connection[obj_key]
                real_obj: QLineEdit = getattr(self, obj_name, False)
                if not real_obj:
                    setattr(self, obj_name, QLineEdit(self.scrollAreaWidgetContents))
                    real_obj: QLineEdit = getattr(self, obj_name)
                    real_obj.setObjectName(obj_name)
                    self.gridLayout_config.addWidget(
                        real_obj, config_index + 1, obj_index, 1, 1
                    )
                real_obj.setText(str(obj_value))
        self.reset_config_grid_layout(config_index) """
        # 设置最小读取间隔
        single_loop_time = global_store.get_config("single_loop_time")
        self.spinBox_single_loop_time.setValue(single_loop_time)


    def add_connection_config_one(self):
        config_index = global_store.get_context("connection_config_index")
        ip_obj_name = f"{IP_BASE_STR}{config_index + 1}"
        port_obj_name = f"{PORT_BASE_STR}{config_index + 1}"
        slot_obj_name = f"{SLOT_BASE_STR}{config_index + 1}"

        for obj_index, (obj_key, obj_name) in enumerate(
            [
                ("ip", ip_obj_name),
                ("port", port_obj_name),
                ("slot", slot_obj_name),
            ]
        ):
            obj_value = ""
            real_obj: QLineEdit = getattr(self, obj_name, None)
            if not real_obj:
                setattr(self, obj_name, QLineEdit(self.scrollAreaWidgetContents))
                real_obj: QLineEdit = getattr(self, obj_name)
                real_obj.setObjectName(obj_name)
                self.gridLayout_config.addWidget(
                    real_obj, config_index + 2, obj_index, 1, 1
                )
            real_obj.setText(str(obj_value))
        self.reset_config_grid_layout(config_index + 1)

    def remove_connection_config_one(self):
        config_index = global_store.get_context("connection_config_index")
        if config_index == 0:
            signals.info_dialog_signal.emit("信息", "配置信息至少要保留一条")
        else:
            ip_obj_name = f"{IP_BASE_STR}{config_index}"
            port_obj_name = f"{PORT_BASE_STR}{config_index}"
            slot_obj_name = f"{SLOT_BASE_STR}{config_index}"
            for obj_index, (obj_key, obj_name) in enumerate(
                [
                    ("ip", ip_obj_name),
                    ("port", port_obj_name),
                    ("slot", slot_obj_name),
                ]
            ):
                real_obj: QLineEdit = getattr(self, obj_name)
                if real_obj:
                    self.gridLayout_config.removeWidget(real_obj)
                    real_obj.deleteLater()
                    setattr(self, obj_name, None)
            self.reset_config_grid_layout(config_index - 1)

    def reset_config_grid_layout(self, config_index_now):
        self.gridLayout_config.removeItem(self.verticalSpacer)
        self.gridLayout_config.addItem(self.verticalSpacer, config_index_now + 2, 0, 1, 1)
        global_store.set_context("connection_config_index", config_index_now)

    

    def append_log_text(self, text: str):
        self.textEdit_log.append(text)

    def show_info_message(self, title: str, info: str):
        """ 显示一个信息弹窗 """
        # QMessageBox.information(self, title, info)
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Information)
        msg.setWindowTitle(title)
        msg.setText(info)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.show() 

    def show_error_message(self, text: str):
        # QMessageBox.critical(self, "错误", text)
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("错误")
        msg.setText(text)
        msg.setStandardButtons(QMessageBox.Ok)
        msg.show() 


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
