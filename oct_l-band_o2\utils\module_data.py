def extract_cfp2_module_data(data_text, module_number):
    """
    从CFP2_DCO模块数据文本中提取指定模块的完整信息

    参数:
        data_text (str): 包含所有模块信息的原始文本
        module_number (int): 要提取的模块编号 (1 或 2)

    返回:
        str: 提取的模块数据，如果找不到则返回None
    """

    if module_number not in [1, 2]:
        return f"错误：模块编号必须是1或2，您输入的是: {module_number}"

    # 寻找指定模块的开始标记
    start_marker = f"CFP2_DCO Module No.{module_number} information:"

    # 寻找下一个模块的开始标记（用于确定当前模块数据的结束位置）
    next_module_number = 2 if module_number == 1 else 3
    end_marker = f"CFP2_DCO Module No.{next_module_number} information:"

    # 找到开始位置
    start_pos = data_text.find(start_marker)
    if start_pos == -1:
        return f"未找到模块 No.{module_number} 的数据"

    # 找到结束位置
    end_pos = data_text.find(end_marker)
    if end_pos == -1:
        # 如果没有找到下一个模块，说明这是最后一个模块，提取到文本末尾
        module_data = data_text[start_pos:].strip()
    else:
        module_data = data_text[start_pos:end_pos].strip()
    return module_data
