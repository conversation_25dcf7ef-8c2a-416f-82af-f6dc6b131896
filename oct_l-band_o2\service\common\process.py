from __future__ import annotations
from typing import TYPE_CHECKING, Union
from collections import defaultdict

from service.common.my_logger import logger

if TYPE_CHECKING:
    from service.http.npb_http import NpbHttpClinet


def judge_is_all_module_stable(
    oct_ip: str,
    npb_http_client_dict: dict[str, NpbHttpClinet],
    oct_slot_set: set[Union[int, str]],
    oct_slot_to_npb_ip_port_tuple_list_mapper: dict[Union[int, str], list[tuple]],
) -> tuple[
    bool, dict[Union[int, str], list[tuple]], dict[Union[int, str], list[tuple]]
]:
    is_module_all_stable_flag = True
    npb_ip_port_to_data_mapper = {}
    unstable_module_info_list = []
    for npb_ip, npb_http_client in npb_http_client_dict.items():
        # 读取数据
        npb_data_list: list[dict] = npb_http_client.query_npb_data()
        # 映射数据
        for npb_doc in npb_data_list:
            npb_data_port = npb_doc["portid"]
            npb_ip_port_to_data_mapper.update({(npb_ip, npb_data_port): npb_doc})
    # 从发送速率和接受速率判断每个配置的端口是否稳定,不稳定就打印日志一直等到稳定
    oct_slot_to_send_error_nums_mapper = defaultdict(list)
    oct_slot_to_receive_error_nums_mapper = defaultdict(list)
    for oct_slot in sorted(oct_slot_set):
        npb_ip_port_tuple_list = oct_slot_to_npb_ip_port_tuple_list_mapper[oct_slot]
        for npb_ip, npb_data_port in npb_ip_port_tuple_list:
            npb_data_doc = npb_ip_port_to_data_mapper[(npb_ip, npb_data_port)]
            senderrors = npb_data_doc["senderrors"]
            rsverrors = npb_data_doc["rsverrors"]
            oct_slot_to_send_error_nums_mapper[oct_slot].append(
                (npb_data_port, senderrors)
            )
            oct_slot_to_receive_error_nums_mapper[oct_slot].append(
                (npb_data_port, rsverrors)
            )
            sendrate = npb_data_doc["sendrate"]
            rsvrate = npb_data_doc["rsvrate"]
            if sendrate == 0 or rsvrate == 0:
                is_module_all_stable_flag = False
                unstable_module_info_list.append({
                    "oct_ip": oct_ip,
                    "oct_slot": oct_slot,
                    "npb_ip": npb_ip,
                    "npb_data_port": npb_data_port,
                    "发送速率": sendrate,
                    "接收速率": rsvrate
                })
                logger.warning(
                    f"未达到稳定状态,oct_ip:{oct_ip},oct_slot:{oct_slot},npb_ip:{npb_ip},\
npb_data_port:{npb_data_port}发送速率为{sendrate},接受速率为{rsvrate},"
                )
            else:
                logger.info(
                    f"已达到稳定状态,oct_ip:{oct_ip},oct_slot:{oct_slot},npb_ip:{npb_ip},\
npb_data_port:{npb_data_port}发送速率为{sendrate},接受速率为{rsvrate}"
                )
    return (
        is_module_all_stable_flag,
        unstable_module_info_list,
        oct_slot_to_send_error_nums_mapper,
        oct_slot_to_receive_error_nums_mapper,
    )


def get_oct_slot_to_send_error_and_receive_error_nums_mapper(
    npb_http_client_dict: dict[str, NpbHttpClinet],
    oct_slot_set: set[Union[int, str]],
    oct_slot_to_npb_ip_port_tuple_list_mapper: dict[Union[int, str], list[tuple]],
) -> tuple[dict[Union[int, str], list[tuple]], dict[Union[int, str], list[tuple]]]:
    npb_ip_port_to_data_mapper = {}
    for npb_ip, npb_http_client in npb_http_client_dict.items():
        # 读取数据
        npb_data_list: list[dict] = npb_http_client.query_npb_data()
        # 映射数据
        for npb_doc in npb_data_list:
            npb_data_port = npb_doc["portid"]
            npb_ip_port_to_data_mapper.update({(npb_ip, npb_data_port): npb_doc})
    oct_slot_to_send_error_nums_mapper = defaultdict(list)
    oct_slot_to_receive_error_nums_mapper = defaultdict(list)
    for oct_slot in sorted(oct_slot_set):
        npb_ip_port_tuple_list = oct_slot_to_npb_ip_port_tuple_list_mapper[oct_slot]
        for npb_ip, npb_data_port in npb_ip_port_tuple_list:
            npb_data_doc = npb_ip_port_to_data_mapper[(npb_ip, npb_data_port)]
            senderrors = npb_data_doc["senderrors"]
            rsverrors = npb_data_doc["rsverrors"]
            oct_slot_to_send_error_nums_mapper[oct_slot].append(
                (npb_ip, npb_data_port, senderrors)
            )
            oct_slot_to_receive_error_nums_mapper[oct_slot].append(
                (npb_ip, npb_data_port, rsverrors)
            )
    return oct_slot_to_send_error_nums_mapper, oct_slot_to_receive_error_nums_mapper
