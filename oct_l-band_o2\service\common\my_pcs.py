from service.third.pcs.PCS import pcs, PCS
import logging


class MyPcs:
    def __init__(self):
        self.pcs: PCS = pcs
        self.logger = None

    def set_logger(self, logger: logging):
        self.logger = logger

    def get_mn(self, sn):
        info_code, res = self.pcs.get_product_info(sn)
        if info_code == 0:
            mn = res["mn"]
            self.logger.info(f"获取mn成功,mn:{mn}")
            return mn
        else:
            raise Exception(f"获取mn失败:{res}")

    def start_process(self, mn, process_name):
        start_code, info = self.pcs.start_process(mn, process_name)
        if start_code == 0:
            self.logger.info(f"MES开始工序成功")
        else:
            raise Exception(f"MES开始工序失败,{info}")

    def upload_data(self, mn: str, file_path: str):
        upload_control_code, info = pcs.upload_test_data(mn, file_path)
        if upload_control_code == 0:
            self.logger.info("MES数据上传成功")
            return upload_control_code
        else:
            raise Exception(f"数据上传MES失败,{info}")

    def end_process(
        self,
        mn: str,
        is_pass: bool,
        defect_category: str = None,
        defect_code: str = None,
    ):
        finish_code, info = pcs.finish_process(
            mn, is_pass, defect_category=defect_category, defect_code=defect_code
        )
        if finish_code == 0:
            self.logger.info(f"mn:{mn}结束工序成功")
            return finish_code
        else:
            raise Exception(f"mn:{mn},结束工序失败,{info}")

    def upload_data_and_end_process(
        self,
        sn: str,
        is_pass: bool,
        # save_file_path: str,
        control_file_path: str,
        defect_category: str = None,
        defect_code: str = None,
    ):
        mn = self.get_mn(sn)
        upload_control_code, _ = pcs.upload_test_data(mn, control_file_path)
        if upload_control_code == 0:
            finish_code, _ = pcs.finish_process(
                mn, is_pass, defect_category=defect_category, defect_code=defect_code
            )
            if finish_code == 0:
                self.logger.info(f"sn:{sn},mn{mn}完成mes数据上传")
            else:
                self.logger.error(f"sn:{sn},mn{mn},关闭工序失败")
        else:
            # if upload_save_code != 0:
            #     self.logger.error(
            #         f"sn:{sn},mn{mn},上传存储文件失败,file_path:{save_file_path}"
            #     )
            if upload_control_code != 0:
                self.logger.error(
                    f"sn:{sn},mn{mn},上传卡控文件失败,file_path:{control_file_path}"
                )
