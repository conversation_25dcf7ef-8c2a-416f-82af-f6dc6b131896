import logging
import os
from logging.handlers import RotatingFileHandler

from config import global_store
from front.core.signals import signals

log_dir = global_store.get_log_dir()
oct_ip = global_store.get_context("oct_ip")


class CustomHandler(logging.Handler):
    """自定义日志 Handler，在日志记录后执行特定操作"""

    def emit(self, record):
        log_message = self.format(record)  # 获取格式化后的日志消息
        self.send_log_info_to_ui(log_message)  # 执行发送QT信号

    def send_log_info_to_ui(self, log_message):
        """在每次日志记录后发送QT信号"""
        signals.log_changed_signal.emit(log_message)


class Logger:
    def __init__(
        self,
        name="oct_mcm_o2",
        log_dir=log_dir,
        level=logging.INFO,
        max_bytes=5 * 1024 * 1024,
        backup_count=50,
    ):
        """
        自定义日志模块
        :param name: 日志记录器的名称
        :param log_dir: 日志存储目录
        :param level: 日志级别（默认 INFO）
        :param max_bytes: 单个日志文件最大字节数（默认 5MB）
        :param backup_count: 备份日志文件的数量（默认 10）
        """
        # 确保日志目录存在
        if not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        log_file = os.path.join(log_dir, f"{name}.log")

        # 创建 Logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)

        # 防止重复添加 Handler
        if not self.logger.handlers:
            # 创建格式化器
            log_format = logging.Formatter(
                "%(asctime)s - %(levelname)s - %(filename)s[%(lineno)d] - %(message)s"
            )

            # 控制台 Handler
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(log_format)
            console_handler.setLevel(logging.INFO)

            # 文件 Handler（带日志轮转）
            file_handler = RotatingFileHandler(
                log_file, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8"
            )
            file_handler.setFormatter(log_format)
            file_handler.setLevel(level)

            # 自定义 Handler
            custom_handler = CustomHandler()
            custom_handler.setFormatter(log_format)

            # 添加 Handler
            self.logger.addHandler(console_handler)
            self.logger.addHandler(file_handler)
            self.logger.addHandler(custom_handler)

    def get_logger(self):
        """获取 Logger 实例"""
        return self.logger


# 全局 Logger 实例（单例模式）
log_file_name = f"oct_mcm_o2_{oct_ip}"
logger = Logger(name=log_file_name).get_logger()
