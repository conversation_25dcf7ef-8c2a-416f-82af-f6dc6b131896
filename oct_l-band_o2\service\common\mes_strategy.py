from __future__ import annotations
from abc import ABC, abstractmethod
from typing import TYPE_CHECKING

from front.core.signals import signals
from service.common.my_pcs import MyPcs
from service.common.my_logger import logger
from service.common.const import PROCESS_NAME

if TYPE_CHECKING:
    from front.main_window import MainWindow


class FeatureMesHanlder(ABC):
    def start_test(self, view: MainWindow) -> str:
        pass

    def before_main_test(self, slot_sn__tuple_list: list[tuple[str, str]]) -> str:
        pass

    def after_main_test(
        self,
        slot_sn_tuple_list: list[tuple[str, str]],
        slot_to_is_all_time_pass_dict: dict[str, bool],
        result_file_path_dict: dict[str, str],
    ):
        pass


class MesFeature1(FeatureMesHanlder):
    def __init__(self):
        self.my_pcs: MyPcs = None
        self.sn_to_mn_mapper = {}

    def start_test(self, view: MainWindow):
        if view.pushButton_login.text() != "登出":
            view.show_error_message("请先登录MES再执行开始挂机")
            return "login first"

    def before_main_test(self, slot_sn__tuple_list: list[tuple[str, str]]) -> str:
        # 对模块在MES系统中获取mn并开始工序
        self.my_pcs = MyPcs()
        self.my_pcs.set_logger(logger)
        for slot, sn in slot_sn__tuple_list:
            mn = self.my_pcs.get_mn(sn)
            signals.table_update_single_row_signal.emit((slot,), {"mn": mn})
            self.my_pcs.start_process(mn, PROCESS_NAME)
            self.sn_to_mn_mapper.update({sn: mn})

    def after_main_test(
        self,
        slot_sn_tuple_list: list[tuple[str, str]],
        slot_to_is_all_time_pass_dict: dict[str, bool],
        result_file_path_dict: dict[str, str],
    ):
        for slot, sn in slot_sn_tuple_list:
            mn = self.sn_to_mn_mapper[sn]
            result_file_path = result_file_path_dict[slot]
            is_pass = slot_to_is_all_time_pass_dict[slot]
            self.my_pcs.upload_data(mn, result_file_path)
            if is_pass:
                self.my_pcs.end_process(mn, is_pass)
            else:
                logger.info(f"slot:{slot},sn:{sn},mn:{mn}未通过测试,不结束工序")


class MesFeature0(FeatureMesHanlder):
    pass


def make_mes_feature_handler(mes_config_value: int) -> FeatureMesHanlder:
    hanlder_mapper = {0: MesFeature0, 1: MesFeature1}
    handler: FeatureMesHanlder = hanlder_mapper[mes_config_value]()
    return handler
