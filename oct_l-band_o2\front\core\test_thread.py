import traceback
from datetime import datetime

from PySide6.QtCore import QThread

from service.main_test import main_test
from front.core.signals import signals
from service.common.my_logger import logger


class MainTestThread(QThread):
    """执行测试的主线程"""

    def __init__(
        self,
        oct_ip: str,
        oct_ssh_port: int,
        oct_npb_config_list: list[dict],
        single_loop_time: int,
        test_start_time: datetime,
    ):
        super().__init__()
        self.oct_ip = oct_ip
        self.oct_ssh_port = oct_ssh_port
        self.oct_npb_config_list = oct_npb_config_list  # 模块连接配置列表
        self.single_loop_time = single_loop_time  # 单轮读取最小时间
        self.test_start_time = test_start_time

    def run(self):
        """线程执行的代码"""
        try:
            main_test(
                self.oct_ip,
                self.oct_ssh_port,
                self.oct_npb_config_list,
                self.single_loop_time,
                self.test_start_time,
            )
        except Exception as e:
            logger.error(f"{e}")
            signals.error_dialog_signal.emit(f"{e}")
            traceback.print_exc()
