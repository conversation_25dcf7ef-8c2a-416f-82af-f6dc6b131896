from collections import defaultdict

import pandas as pd

oct_npb_mapper_path = "./OCT-NPB映射表.xlsx"


class OctNpbMapper:
    def __init__(self):
        self.df = pd.read_excel(oct_npb_mapper_path, sheet_name=0)
        self.df.fillna("", inplace=True)
        self.config_list = []
        self.oct_ip_port_to_config_list_mapper = defaultdict(list)

    def check_config_and_generate_config(self):
        unqie_key_set = set()
        for index, config_row in self.df.iterrows():
            if config_row["npb_ip"]:
                unique_tuple = (config_row["npb_ip"], config_row["npb_data_port"])
                excel_index = index + 2
                config_row = dict(config_row)
                config_row.update({"excel_index": excel_index})
                if unique_tuple in unqie_key_set:
                    raise ValueError(
                        f"文件{oct_npb_mapper_path},第{excel_index}行npb_ip和npb_data_port配置重复"
                    )
                unqie_key_set.add(unique_tuple)
                self.config_list.append(config_row)
                self.oct_ip_port_to_config_list_mapper[
                    (config_row["oct_ip"], config_row["oct_ssh_port"])
                ].append(config_row)

if __name__ == "__main__":
    oct_npb_mapper = OctNpbMapper()
    oct_npb_mapper.check_config_and_generate_config()
    print(oct_npb_mapper.oct_ip_port_to_config_list_mapper)