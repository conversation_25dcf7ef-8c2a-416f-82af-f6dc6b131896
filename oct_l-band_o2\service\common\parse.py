import struct
import math
from ctypes import c_uint16


def signed_num(num, bit):
    if bit <= 32:
        sign = num >> (bit - 1)
        if sign == 0:
            num = num
        else:
            num = -((num ^ (0xFFFFFFFF >> (32 - bit))) + 1)
    else:
        num = 0
        print(" error:bit > 32")
    return num


def uint16_2_to_float(value: list):
    return struct.unpack("<f", struct.pack("<HH", value[1], value[0]))[0]


def signed_f16_hex_2_float(h):
    if type(h) == type(1):  # 0724 HE
        i = h
    else:
        i = int(h, 16)
    exponet_sign = i >> 15
    exponet = (i >> 10) & 0b11111
    mantissa = i & 0x3FF
    if exponet_sign == 1:
        exponet = -exponet
    return mantissa * 0.1 * 10**exponet


def uint8_4_to_float(value: list):
    return struct.unpack(
        "<f", struct.pack(">HH", (value[0] << 8) + value[1], (value[2] << 8) + value[3])
    )[0]


def hex_str_to_float(value: str) -> float:
    return struct.unpack(">f", bytes.fromhex(value))[0]


def power_calc(buff: list = None) -> float:
    pwr_mW: int = c_uint16((buff[0] << 8) + buff[1]).value
    if pwr_mW > 0:
        return round(10 * math.log10(pwr_mW / 10000), 3)
    else:
        return -40
