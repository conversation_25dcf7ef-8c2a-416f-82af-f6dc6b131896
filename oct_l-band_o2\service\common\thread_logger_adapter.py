from logging import LoggerAdapter

class ThreadLoggerAdapter(LoggerAdapter):

    def process(self, msg, kwargs):
        """在日志消息前添加信息

        Args:
            msg (str): 记录的日志信息

        在日志前添加办卡名称，板卡IP，模块SN，模块PN等信息
        """
        part_1 = f"ip-{self.extra['ip']},port-{self.extra['port']},slot-{self.extra['slot']}"
        part_3 = f" - {msg}"
        result = ""
        if self.extra.get('sn'):
            part_2 = f",模块sn-{self.extra['sn']}"
            result = f"{part_1}{part_2}{part_3}"
        else:
            result = f"{part_1}{part_3}"
        return (result, kwargs)