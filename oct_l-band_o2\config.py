import json
import os

config_path = "./config.json"
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

class Store:
    def __init__(self):
        with open(config_path, "r", encoding="utf-8") as file:
            config_dict:dict = json.load(file)
        self.config_dict = config_dict
        self.store = {}
    
    def set_context(self, k, v):
        self.store[k] = v

    def get_context(self, k):
        return self.store[k]
    
    def remove_context(self, k):
        try:
            del self.store[k]
        except Exception as e:
            pass
    
    def get_whole_config(self) -> dict:
        return self.config_dict


    def save_whole_config(self, new_config_dict):
        with open(config_path, "w", encoding="utf-8") as file:
            json.dump(
                new_config_dict, file, ensure_ascii=False, indent=4
            )  # 确保中文不转义，格式化输出
        self.config_dict = new_config_dict

    def get_base_dir():
        return BASE_DIR

    def get_log_dir(self):
        log_dir = f'./{self.config_dict["log_dir"]}'
        return log_dir

    def get_config(self, key):
        return self.config_dict[key]

    def set_config(self, key, value):
        self.config_dict[key] = value

global_store = Store()   ## 单例模式
global_store.set_context("connection_config_index", 0)