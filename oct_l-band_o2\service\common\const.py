PROCESS_NAME = "测试-温循测试"

EXCEL_DATETIME_FORMAT = "%Y/%m/%d %H:%M:%S"
EXCEL_NAME_TIME_FORMAT = "%Y_%m_%d__%H_%M_%S"

STANDARD_DATETIME_FORMAT = "%Y-%m-%d %H:%M:%S"

BASE_OUTPUT_FILE_PATH = "./output_file"

SINGLE_VALUE_KEY_LIST = [
    "Module Temperature",
    "Module Power Supply",
    "SOA Bias",
    "Tx Laser Bias",
    "Network TX Laser Temp",
    "RX Current Freq",
    "Current Output Power",
    "Current Input Power",
    "Network Current BER",
    "Network FEC Uncorr Blk Cnt",
    "Network FEC Uncorr Blk Reset"
]

MULTI_VALUE_KEY_LIST = [
    "Client Tx FEC Uncorr Blk SMR",
    "Client Tx Post FEC Ber PM-Int",
]

CONTROL_VALUE_LIST = [
    {"name": "Client Tx FEC Uncorr Blk SMR[1]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx FEC Uncorr Blk SMR[2]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx FEC Uncorr Blk SMR[3]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx FEC Uncorr Blk SMR[4]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx Post FEC Ber PM-Int[1]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx Post FEC Ber PM-Int[2]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx Post FEC Ber PM-Int[3]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Client Tx Post FEC Ber PM-Int[4]", "min_value": -999, "max_value": -999, "value_type": "float",
     "type": "Increase"},
    {"name": "Module Temperature", "min_value": 0, "max_value": 70, "value_type": "float"},
]

# NPB网页默认用户名和默认密码,若没有在config.json中配置npb_http_config中对应IP的用户名和密码则用此默认用户名和密码
NPB_DEFAULT_USERNAME = "admin"
NPB_DEFAULT_PASSWORD = "Admin_123"
# OCT设备SSH默认用户名和密码
OCT_DEFAULT_USERNAME = "admin"
OCT_DEFAULT_PASSWROD = "Admin_123"

# 两次判断模块是否稳定之间的时间间隔,单位:秒
JUDGE_ALL_MODULE_STABLE_TIME_GAP = 10
# 所有模块全部稳定后再清零操作前的等待时间,单位:秒
SLEEP_TIME_AFTER_ALL_MODULE_STABLE = 120

OUTPUT_KEY_LIST = [
    "datetime",
    "Module Temperature",
    "Module Power Supply",
    "SOA Bias",
    "Tx Laser Bias",
    "Network TX Laser Temp",
    "RX Current Freq",
    "Current Output Power",
    "Current Input Power",
    "Network Current BER",
    "Network FEC Uncorr Blk Cnt",
    "Network FEC Uncorr Blk Reset",
    "Client Tx FEC Uncorr Blk SMR[1]",
    "Client Tx FEC Uncorr Blk SMR[2]",
    "Client Tx FEC Uncorr Blk SMR[3]",
    "Client Tx FEC Uncorr Blk SMR[4]",
    "Client Tx Post FEC Ber PM-Int[1]",
    "Client Tx Post FEC Ber PM-Int[2]",
    "Client Tx Post FEC Ber PM-Int[3]",
    "Client Tx Post FEC Ber PM-Int[4]",
]

ERROR_KEY_LISY = [
    "datetime",
    "name",
    "value",
    "min_value",
    "max_value",
    "is_pass"
]

RESULT_STR_MAPPER = {
    True: "通过测试",
    False: "未通过测试"
}
